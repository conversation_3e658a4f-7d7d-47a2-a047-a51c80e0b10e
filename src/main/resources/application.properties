stage=${STAGE:local}

# Logging
logging.level=INFO
logging.level.org.hibernate=WARN
logging.level.org.springframework=INFO

# Spring
spring.main.banner-mode=off

management.server.port=8080
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=always

server.compression.enabled=true
server.compression.mime-types=application/json

### Datasource
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=100

spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USER}
spring.datasource.password=${DB_PASS}
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Kafka Configuration
spring.kafka.enabled=${KAFKA_ENABLED:true}

# liquibase
spring.liquibase.change-log=${LIQUIBASE_CHANGE_LOG:classpath:sql/changelog-master.xml}
spring.liquibase.enabled=${LIQUIBASE_ENABLE:false}

#kafka topic
upm.tema.notification.topic=${UPM_TEMA_NOTIFICATION_TOPIC:upm.tema.notification}

#kafka consumer config
spring.kafka.consumer.auto-offset-reset=${CONSUMER_AUTO_OFFSET_RESET:latest}
spring.kafka.consumer.heartbeat-interval=${CONSUMER_HEARTBEAT_INTERVAL:20000}
spring.kafka.consumer.properties.session.timeout.ms=${CONSUMER_SESSION_TIMEOUT:60000}
spring.kafka.consumer.enable-auto-commit=${CONSUMER_AUTO_COMMIT:false}
spring.kafka.consumer.client-id=${CONSUMER_CLIENT_ID:tema-svc-local}
spring.kafka.consumer.group-id=${CONSUMER_GROUP_ID:tema-svc-local}

#kafka producer config
spring.kafka.bootstrap-servers=${KAFKA_SERVER:localhost:9092}
spring.kafka.producer.client-id=${PRODUCER_CLIENT_ID:tema-svc-local}
spring.kafka.producer.retries=${PRODUCER_RETRIES:3}

#kafka ssl config
spring.kafka.key.truststore.location=/etc/tls/kafka-ca-certs/kafka-ca.jks
spring.kafka.security.protocol=SASL_SSL
spring.kafka.ssl.enabled.protocols=TLSv1.2,TLSv1.3
spring.kafka.ssl.trust-store-password=${KEYSTORE_PASSWORD}
spring.kafka.ssl.truststore.type=JKS

spring.kafka.properties.security.protocol=SASL_SSL
spring.kafka.properties.sasl.mechanism=SCRAM-SHA-512
spring.kafka.properties.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="${KAFKA_USERNAME}" password="${KAFKA_PASSWORD}";

# Web Server
btpns-platform.http-server.default.io-threads=${SERVER_IO_THREADS:2}
btpns-platform.http-server.default.worker-threads=${SERVER_WORKER_THREADS:0}

channel.id=${CHANNEL_ID:6022}
node=${X_NODE:BTPNS}
terminal.id=${TERMINAL_ID:bintan.tema-svc}
#mail.uri=${MAIL_URL:https://mail-int-beta.apps.nww.syariahbtpn.com/}
mail.uri=${MAIL_URL:https://api-beta-v2.apps.btpnsyariah.com/int/notification/2.0.0/}
#mail.uri=${MAIL_URL:https://api-dirty-v2.apps.south.syariahbtpn.com/int/notification/2.0.0/}
#mail.uri=https://deelay.me/40000/https://api-dirty-v2.apps.nww.syariahbtpn.com/int/notification/2.0.0/
mail.api.version=${MAIL_API_VERSION:1.3.0}
#mail.sender=${MAIL_SENDER:<EMAIL>}
mail.sender=${MAIL_SENDER:<<EMAIL>>}
mail.target=${MAIL_TARGET:<EMAIL>}
send.data.core.mail.target=${SEND_DATA_CORE_MAIL_TARGET:<EMAIL>}
send.data.core.mail.cc=${SEND_DATA_CORE_MAIL_CC:}
mail.cc=${MAIL_CC: }
mail.bcc=${MAIL_BCC: }
mail.enable=${MAIL_ENABLE:true}
mail.cc.enable=${MAIL_CC_ENABLE:false}
terminal.name=${TERMINAL_NAME:temasvc}
acq.id=${ACQ_ID:547}
orgunit.id=${ORGUNIT_ID:547}
header.api_key=${HEADER_API_KEY}

#scheduler
cron.schedule.retryemail=${SCHEDULE_CRON_RETRYEMAIL:0 0/15 * * * *}
cron.schedule.uarreminder=${SCHEDULE_CRON_UARREMINDER:0 0 0 5 1,4,7,10 ?}
cron.schedule.monthlyreminder=${SCHEDULE_CRON_MONTHLYREMINDER:0 0 0 10 * ?}
cron.schedule.senddatacore=${SCHEDULE_CRON_SENDDATACORE:0 10 5 1 * *}
cron.schedule.reminderapprovalpuk=${SCHEDULE_CRON_REMINDERAPPROVAL:0 0 5-17 * * 1-5}

#url.fuid.detail=https://tema-web-upm-dirty.apps.nww.syariahbtpn.com/form/fuid/
url.fuid.detail=${URL_FUID_DETAIL:https://tema-web-upm-beta.apps.nww.syariahbtpn.com/form/fuid/}
#url.param.detail=https://tema-web-upm-dirty.apps.nww.syariahbtpn.com/form/fsp/
url.param.detail=${URL_PARAM_DETAIL:https://tema-web-upm-beta.apps.nww.syariahbtpn.com/form/fsp/}
url.uar.detail=${URL_UAR_DETAIL:https://tema-web-upm-beta.apps.south.syariahbtpn.com/rincian-pemantauan-user-id/}

# Prospera Uri
prospera.router.uri=${PROSPERA_ROUTER_URL:https://router-prospera2-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com/prosperarest/services/}

minio.uri=${MINIO_URI:https://storage-dirty.apps.nww.syariahbtpn.com}
minio.default.bucket.name=${BUCKET_NAME:bucket01}
image.base.uri=${IMAGE_BASE_URI:https://storage-dirty.apps.nww.syariahbtpn.com}

spesimen.file.path=${SPESIMEN_FILE_PATH:UPM/TEMA/Template_Spesimen.pdf}
spesimen.file.name=${SPESIMEN_FILE_NAME:Template_Spesimen.pdf}

path.logo.btpns=classpath:image/btpns-logo.png

user.whitelist.menu.prospera.by.nik=${USER_WHITELIST_MENU_PROSPERA_BY_NIK:all}
user.whitelist.menu.prospera.by.officecode=${USER_WHITELIST_MENU_PROSPERA_BY_OFFICECODE:all}

max.upm.limit.for.bwmp.nominal.input=${MAX_UPM_LIMIT_FOR_BWMP_NOMINAL_INPUT:100000000}
query.limit=${QUERY_LIMIT:1000}

#Springdoc config
springdoc.swagger-ui.enabled=${SPRINGDOC_UI_TOGGLE:false}
springdoc.swagger-ui.doc-expansion=none

spring.servlet.multipart.max-file-size = 20MB
spring.servlet.multipart.max-request-size = 20MB

enable.log.base64=${ENABLE_LOG_BASE64:false}