package com.btpns.fin.model.request;

import com.btpns.fin.model.entity.TrxFuidApproval;

import java.time.LocalDateTime;
import java.util.Date;

public class TrxFuidRequestDTO {
   
    private String ticketId;

    private LocalDateTime createDateTime;

    private String tujuan;

    private String alasan;

    private Date tanggalEfektif;

    
    private String dataNik;

    
    private String dataNamaLengkap;

    
    private String dataJabatan;

    private String dataKodeCabang;

    private String dataNamaCabang;

    private String dataTelepon;

    
    private String dataNamaVendor;

    private String nikRequester;

    private String aplikasi;

    private String tingkatan;

    private String statusMasaBerlaku;

    private Date masaBerlakuSampai;

    private String alasanPengajuan;

    private String infoTambahan;

    private String tipeKewenanganLimit;

    private TrxFuidApproval trxFuidApproval;

    //@Convert(converter = VarbinaryConverter.class)
    private String attachment;

    private String fileName;

    private String dataUserId;

    private String dataEmail;

    private String tipeLimitTransaksi;

    private Double nominalTransaksi;

    private Double nominalTransaksiUPM;

    private String unitKerjaLama;

    private String unitKerjaBaru;

    private String tipeKaryawanBaru;

    private String requestId;

    private String occupationDesc;

    private String organization;

    private String location;

    private String inputType;

    private String batchId;

    private String role;

    private Integer isInActivePersonnelProspera;

    private String picEmailGroup;
    private String picEmailGroupName;
    private String picEmailGroupOccupation;
    private String altPicEmailGroup;
    private String altPicEmailGroupName;
    private String altPicEmailGroupOccupation;
    private String pukVendorNIK;
    private String pukVendorName;
    private String pukVendorOccupation;

    public void setTicketId(String ticketId) {this.ticketId = ticketId;}

    public void setCreateDateTime(LocalDateTime createDateTime) {this.createDateTime = createDateTime;}

    public void setTujuan(String tujuan) {this.tujuan = tujuan;}

    public void setAlasan(String alasan) {this.alasan = alasan;}

    public void setTanggalEfektif(Date tanggalEfektif) {this.tanggalEfektif = tanggalEfektif;}

    public void setDataNik(String dataNik) {this.dataNik = dataNik;}

    public void setDataNamaLengkap(String dataNamaLengkap) {this.dataNamaLengkap = dataNamaLengkap;}

    public void setDataJabatan(String dataJabatan) {this.dataJabatan = dataJabatan;}

    public void setDataKodeCabang(String dataKodeCabang) {this.dataKodeCabang = dataKodeCabang;}

    public void setDataNamaCabang(String dataNamaCabang) {this.dataNamaCabang = dataNamaCabang;}

    public void setDataTelepon(String dataTelepon) {this.dataTelepon = dataTelepon;}

    public void setDataNamaVendor(String dataNamaVendor) {this.dataNamaVendor = dataNamaVendor;}

    public void setNikRequester(String nikRequester) {this.nikRequester = nikRequester;}

    public void setAplikasi(String aplikasi) {this.aplikasi = aplikasi;}

    public void setTingkatan(String tingkatan) {this.tingkatan = tingkatan;}

    public void setStatusMasaBerlaku(String statusMasaBerlaku) {this.statusMasaBerlaku = statusMasaBerlaku;}

    public void setMasaBerlakuSampai(Date masaBerlakuSampai) {this.masaBerlakuSampai = masaBerlakuSampai;}

    public void setAlasanPengajuan(String alasanPengajuan) {this.alasanPengajuan = alasanPengajuan;}

    public void setInfoTambahan(String infoTambahan) {this.infoTambahan = infoTambahan;}

    public void setAttachment(String attachment) {this.attachment = attachment;}

    public void setTrxFuidApproval(TrxFuidApproval trxFuidApproval) { this.trxFuidApproval = trxFuidApproval; }

    public String getTicketId() {return ticketId;}

    public LocalDateTime getCreateDateTime() {return createDateTime;}

    public String getTujuan() {return tujuan;}

    public String getAlasan() {return alasan;}

    public Date getTanggalEfektif() {return tanggalEfektif;}

    public String getDataNik() {return dataNik;}

    public String getDataNamaLengkap() {return dataNamaLengkap;}

    public String getDataJabatan() {return dataJabatan;}

    public String getDataKodeCabang() {return dataKodeCabang;}

    public String getDataNamaCabang() {return dataNamaCabang;}

    public String getDataTelepon() {return dataTelepon;}

    public String getDataNamaVendor() {return dataNamaVendor;}

    public String getNikRequester() {return nikRequester;}

    public String getAplikasi() {return aplikasi;}

    public String getTingkatan() {return tingkatan;}

    public String getStatusMasaBerlaku() {return statusMasaBerlaku;}

    public Date getMasaBerlakuSampai() {return masaBerlakuSampai;}

    public String getAlasanPengajuan() {return alasanPengajuan;}

    public String getInfoTambahan() {return infoTambahan;}

    public String getAttachment() {return attachment;}

    public TrxFuidApproval getTrxFuidApproval() { return trxFuidApproval; }

    public String getDataUserId() {
        return dataUserId;
    }

    public void setDataUserId(String dataUserId) {
        this.dataUserId = dataUserId;
    }

    public String getDataEmail() {
        return dataEmail;
    }

    public void setDataEmail(String dataEmail) {
        this.dataEmail = dataEmail;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTipeLimitTransaksi() {
        return tipeLimitTransaksi;
    }

    public void setTipeLimitTransaksi(String tipeLimitTransaksi) {
        this.tipeLimitTransaksi = tipeLimitTransaksi;
    }

    public Double getNominalTransaksi() {
        return nominalTransaksi;
    }

    public void setNominalTransaksi(Double nominalTransaksi) {
        this.nominalTransaksi = nominalTransaksi;
    }

    public Double getNominalTransaksiUPM() {
        return nominalTransaksiUPM;
    }

    public void setNominalTransaksiUPM(Double nominalTransaksiUPM) {
        this.nominalTransaksiUPM = nominalTransaksiUPM;
    }

    public String getUnitKerjaLama() {
        return unitKerjaLama;
    }

    public void setUnitKerjaLama(String unitKerjaLama) {
        this.unitKerjaLama = unitKerjaLama;
    }

    public String getUnitKerjaBaru() {
        return unitKerjaBaru;
    }

    public void setUnitKerjaBaru(String unitKerjaBaru) {
        this.unitKerjaBaru = unitKerjaBaru;
    }

    public String getTipeKewenanganLimit() {
        return tipeKewenanganLimit;
    }

    public void setTipeKewenanganLimit(String tipeKewenanganLimit) {
        this.tipeKewenanganLimit = tipeKewenanganLimit;
    }

    public String getTipeKaryawanBaru() {
        return tipeKaryawanBaru;
    }

    public void setTipeKaryawanBaru(String tipeKaryawanBaru) {
        this.tipeKaryawanBaru = tipeKaryawanBaru;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getOccupationDesc() { return occupationDesc; }

    public void setOccupationDesc(String occupationDesc) { this.occupationDesc = occupationDesc; }

    public String getOrganization() { return organization; }

    public void setOrganization(String organization) { this.organization = organization; }

    public String getLocation() { return location; }

    public void setLocation(String location) { this.location = location; }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getIsInActivePersonnelProspera() {
        return isInActivePersonnelProspera;
    }

    public void setIsInActivePersonnelProspera(Integer isInActivePersonnelProspera) {
        this.isInActivePersonnelProspera = isInActivePersonnelProspera;
    }

    public String getPicEmailGroup() {
        return picEmailGroup;
    }

    public void setPicEmailGroup(String picEmailGroup) {
        this.picEmailGroup = picEmailGroup;
    }

    public String getPicEmailGroupName() {
        return picEmailGroupName;
    }

    public void setPicEmailGroupName(String picEmailGroupName) {
        this.picEmailGroupName = picEmailGroupName;
    }

    public String getPicEmailGroupOccupation() {
        return picEmailGroupOccupation;
    }

    public void setPicEmailGroupOccupation(String picEmailGroupOccupation) {
        this.picEmailGroupOccupation = picEmailGroupOccupation;
    }

    public String getAltPicEmailGroup() {
        return altPicEmailGroup;
    }

    public void setAltPicEmailGroup(String altPicEmailGroup) {
        this.altPicEmailGroup = altPicEmailGroup;
    }

    public String getAltPicEmailGroupName() {
        return altPicEmailGroupName;
    }

    public void setAltPicEmailGroupName(String altPicEmailGroupName) {
        this.altPicEmailGroupName = altPicEmailGroupName;
    }

    public String getAltPicEmailGroupOccupation() {
        return altPicEmailGroupOccupation;
    }

    public void setAltPicEmailGroupOccupation(String altPicEmailGroupOccupation) {
        this.altPicEmailGroupOccupation = altPicEmailGroupOccupation;
    }

    public String getPukVendorNIK() {
        return pukVendorNIK;
    }

    public void setPukVendorNIK(String pukVendorNIK) {
        this.pukVendorNIK = pukVendorNIK;
    }

    public String getPukVendorName() {
        return pukVendorName;
    }

    public void setPukVendorName(String pukVendorName) {
        this.pukVendorName = pukVendorName;
    }

    public String getPukVendorOccupation() {
        return pukVendorOccupation;
    }

    public void setPukVendorOccupation(String pukVendorOccupation) {
        this.pukVendorOccupation = pukVendorOccupation;
    }
}
