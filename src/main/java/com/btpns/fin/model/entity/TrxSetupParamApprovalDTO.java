package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.math.BigInteger;
import java.time.LocalDateTime;


public class TrxSetupParamApprovalDTO {

    private BigInteger id;

    private String ticketId;

    private String currentState;

    
    private LocalDateTime currentStateDT;

    
    
    private TrxSetupParamRequest trxSetupParamRequest;

    private String puk1NIK;
    private String puk1Name;
    private String puk1Occupation;

    private String puk1Status;

    
    private LocalDateTime puk1Dt;

    private String puk1Notes;

    private String puk1DelegationId;
    private Integer puk1ApprovalReminder;

    private String puk2NIK;
    private String puk2Name;
    private String puk2Occupation;

    private String puk2Status;

    
    private LocalDateTime puk2Dt;

    private String puk2Notes;

    private String puk2DelegationId;
    private Integer puk2ApprovalReminder;

    private String upmInputNIK;

    private String upmInputStatus;

    
    private LocalDateTime upmInputDt;

    private String upmInputNotes;

    private String upmCheckerNIK;

    private String upmCheckerStatus;

    
    private LocalDateTime upmCheckerDt;

    private String upmCheckerNotes;

    private String upmInputAttachment;

    private int slaValue;

    private String slaInfo;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public LocalDateTime getCurrentStateDT() {
        return currentStateDT;
    }

    public void setCurrentStateDT(LocalDateTime currentStateDT) {
        this.currentStateDT = currentStateDT;
    }

    public TrxSetupParamRequest getTrxSetupParamRequest() { return trxSetupParamRequest; }

    public void setTrxSetupParamRequest(TrxSetupParamRequest trxSetupParamRequest) { this.trxSetupParamRequest = trxSetupParamRequest; }

    public String getPuk1NIK() {
        return puk1NIK;
    }

    public void setPuk1NIK(String puk1NIK) {
        this.puk1NIK = puk1NIK;
    }

    public String getPuk2NIK() {
        return puk2NIK;
    }

    public void setPuk2NIK(String puk2NIK) {
        this.puk2NIK = puk2NIK;
    }

    public String getPuk1Name() {
        return puk1Name;
    }

    public void setPuk1Name(String puk1Name) {
        this.puk1Name = puk1Name;
    }

    public String getPuk2Name() {
        return puk2Name;
    }

    public void setPuk2Name(String puk2Name) {
        this.puk2Name = puk2Name;
    }

    public String getPuk1Occupation() {
        return puk1Occupation;
    }

    public void setPuk1Occupation(String puk1Occupation) {
        this.puk1Occupation = puk1Occupation;
    }

    public String getPuk2Occupation() {
        return puk2Occupation;
    }

    public void setPuk2Occupation(String puk2Occupation) {
        this.puk2Occupation = puk2Occupation;
    }

    public String getPuk1Status() {
        return puk1Status;
    }

    public void setPuk1Status(String puk1Status) {
        this.puk1Status = puk1Status;
    }

    public LocalDateTime getPuk1Dt() {
        return puk1Dt;
    }

    public void setPuk1Dt(LocalDateTime puk1Dt) {
        this.puk1Dt = puk1Dt;
    }

    public String getPuk1Notes() {
        return puk1Notes;
    }

    public void setPuk1Notes(String puk1Notes) {
        this.puk1Notes = puk1Notes;
    }

    public String getPuk2Status() {
        return puk2Status;
    }

    public void setPuk2Status(String puk2Status) {
        this.puk2Status = puk2Status;
    }

    public LocalDateTime getPuk2Dt() {
        return puk2Dt;
    }

    public void setPuk2Dt(LocalDateTime puk2Dt) {
        this.puk2Dt = puk2Dt;
    }

    public String getPuk2Notes() {
        return puk2Notes;
    }

    public void setPuk2Notes(String puk2Notes) {
        this.puk2Notes = puk2Notes;
    }

    public Integer getPuk1ApprovalReminder() {
        return puk1ApprovalReminder;
    }

    public void setPuk1ApprovalReminder(Integer puk1ApprovalReminder) {
        this.puk1ApprovalReminder = puk1ApprovalReminder;
    }

    public Integer getPuk2ApprovalReminder() {
        return puk2ApprovalReminder;
    }

    public void setPuk2ApprovalReminder(Integer puk2ApprovalReminder) {
        this.puk2ApprovalReminder = puk2ApprovalReminder;
    }

    public String getUpmInputNIK() {
        return upmInputNIK;
    }

    public void setUpmInputNIK(String upmInputNIK) {
        this.upmInputNIK = upmInputNIK;
    }

    public String getUpmInputStatus() {
        return upmInputStatus;
    }

    public void setUpmInputStatus(String upmInputStatus) {
        this.upmInputStatus = upmInputStatus;
    }

    public LocalDateTime getUpmInputDt() {
        return upmInputDt;
    }

    public void setUpmInputDt(LocalDateTime upmInputDt) {
        this.upmInputDt = upmInputDt;
    }

    public String getUpmInputNotes() {
        return upmInputNotes;
    }

    public void setUpmInputNotes(String upmInputNotes) {
        this.upmInputNotes = upmInputNotes;
    }

    public String getUpmCheckerNIK() {
        return upmCheckerNIK;
    }

    public void setUpmCheckerNIK(String upmCheckerNIK) {
        this.upmCheckerNIK = upmCheckerNIK;
    }

    public String getUpmCheckerStatus() {
        return upmCheckerStatus;
    }

    public void setUpmCheckerStatus(String upmCheckerStatus) {
        this.upmCheckerStatus = upmCheckerStatus;
    }

    public LocalDateTime getUpmCheckerDt() {
        return upmCheckerDt;
    }

    public void setUpmCheckerDt(LocalDateTime upmCheckerDt) {
        this.upmCheckerDt = upmCheckerDt;
    }

    public String getUpmCheckerNotes() {
        return upmCheckerNotes;
    }

    public void setUpmCheckerNotes(String upmCheckerNotes) {
        this.upmCheckerNotes = upmCheckerNotes;
    }

    public String getPuk1DelegationId() {
        return puk1DelegationId;
    }

    public void setPuk1DelegationId(String puk1DelegationId) {
        this.puk1DelegationId = puk1DelegationId;
    }

    public String getPuk2DelegationId() {
        return puk2DelegationId;
    }

    public void setPuk2DelegationId(String puk2DelegationId) {
        this.puk2DelegationId = puk2DelegationId;
    }

    public String getUpmInputAttachment() {
        return upmInputAttachment;
    }

    public void setUpmInputAttachment(String upmInputAttachment) {
        this.upmInputAttachment = upmInputAttachment;
    }

    public int getSlaValue() {
        return slaValue;
    }

    public void setSlaValue(int slaValue) {
        this.slaValue = slaValue;
    }

    public String getSlaInfo() {
        return slaInfo;
    }

    public void setSlaInfo(String slaInfo) {
        this.slaInfo = slaInfo;
    }
}
