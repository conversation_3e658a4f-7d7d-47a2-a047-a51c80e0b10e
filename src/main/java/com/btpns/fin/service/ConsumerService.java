package com.btpns.fin.service;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.EmailNotification;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import static com.btpns.fin.helper.Constants.*;

@Service
public class ConsumerService {
    private static final Logger logger = LoggerFactory.getLogger(ConsumerService.class);

    @Autowired
    private Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    EmailNotificationService emailNotificationService;

    @KafkaListener(topics = "${upm.tema.notification.topic}", autoStartup = "false")
    public void upmTemaNotificationListener(@Payload String payload,
                                            Acknowledgment acknowledgment) {
        logger.info("Received data from topic: upm.tema.notification Data: {}", payload);
        try {
            EmailNotification emailNotification = gson.fromJson(payload, EmailNotification.class);
            if(!EMAIL_DUMMY_USER_TEMA.contains(emailNotification.getEmailDestination())){
                emailNotificationService.sendEmail(emailNotification, EMPTY);
            }else {
                logger.info("exception sends dummy email of tema user, email destination : {}", emailNotification.getEmailDestination());
            }
        } catch (Exception e) {
            logger.error("Fail to send email {} ", payload, e);
        } finally {
            acknowledgment.acknowledge();
        }
    }
}
